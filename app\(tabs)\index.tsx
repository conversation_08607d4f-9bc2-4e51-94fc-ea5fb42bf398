import type { FirebaseAuthTypes } from '@react-native-firebase/auth';
import type { User as WebUser } from 'firebase/auth';
import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { LoginPopup, RegisterPopup } from '../../components/auth';
import { FuturisticTouchable } from '../../components/FuturisticTouchable';
import { ThemedText } from '../../components/ThemedText';
import { ThemedView } from '../../components/ThemedView';
import auth from '../firebase/config';

type AuthUser = FirebaseAuthTypes.User | WebUser;

export default function HomePage() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [showLogin, setShowLogin] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged((currentUser: AuthUser | null) => {
      setUser(currentUser);
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <ThemedText style={styles.loadingText}>Loading...</ThemedText>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ThemedView style={styles.content}>
        <ThemedText style={styles.title}>FeeFence</ThemedText>
        <ThemedText style={styles.subtitle}>
          Take control of your app usage
        </ThemedText>

        {!user ? (
          <ThemedView style={styles.authButtons}>
            <FuturisticTouchable
              style={styles.button}
              onPress={() => setShowLogin(true)}
            >
              <ThemedText style={styles.buttonText}>Sign In</ThemedText>
            </FuturisticTouchable>

            <FuturisticTouchable
              style={[styles.button, styles.registerButton]}
              onPress={() => setShowRegister(true)}
            >
              <ThemedText style={styles.buttonText}>Create Account</ThemedText>
            </FuturisticTouchable>
          </ThemedView>
        ) : (
          <ThemedView style={styles.welcomeContainer}>
            <ThemedText style={styles.welcomeText}>
              Welcome, {user.email}
            </ThemedText>
          </ThemedView>
        )}
      </ThemedView>

      <LoginPopup
        isVisible={showLogin}
        onClose={() => setShowLogin(false)}
        onSwitchToRegister={() => {
          setShowLogin(false);
          setShowRegister(true);
        }}
      />

      <RegisterPopup
        isVisible={showRegister}
        onClose={() => setShowRegister(false)}
        onSwitchToLogin={() => {
          setShowRegister(false);
          setShowLogin(true);
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 40,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    opacity: 0.8,
    marginBottom: 48,
    textAlign: 'center',
  },
  authButtons: {
    width: '100%',
    maxWidth: 300,
    gap: 16,
  },
  button: {
    height: 56,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#007AFF',
  },
  registerButton: {
    backgroundColor: '#5856D6',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  loadingText: {
    fontSize: 18,
    textAlign: 'center',
    marginTop: 20,
  },
  welcomeContainer: {
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: 20,
    textAlign: 'center',
  },
});
