import { auth } from './config';
import { Platform } from 'react-native';

export const testFirebaseConnection = async () => {
  try {
    console.log('Testing Firebase connection...');
    console.log('Platform:', Platform.OS);
    
    if (Platform.OS === 'web') {
      // Web platform test
      const webAuth = auth as any;
      console.log('Firebase Web Auth initialized:', !!webAuth);
      console.log('Current user:', webAuth.currentUser);
    } else {
      // Mobile platform test
      const mobileAuth = auth as any;
      console.log('Firebase Mobile Auth initialized:', !!mobileAuth);
      console.log('Current user:', mobileAuth.currentUser);
    }
    
    return { success: true, message: 'Firebase connection successful' };
  } catch (error) {
    console.error('Firebase connection error:', error);
    return { success: false, error: error.message };
  }
};
