import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { getApps, initializeApp } from 'firebase/app';
import { getAuth as getWebAuth, Auth as WebAuth } from 'firebase/auth';
import { Platform } from 'react-native';

// Firebase configuration for web platform only
const firebaseConfig = {
  apiKey: "AIzaSyAsAOt7GGkWKKJLZp3MVdozcUB_ghoLqOc",
  authDomain: "feefence.firebaseapp.com",
  projectId: "feefence",
  storageBucket: "feefence.appspot.com",
  messagingSenderId: "83272932146",
  appId: "1:83272932146:web:8919ce6c404d8d1497d52b",
  measurementId: "G-T0CJFQW6JB"
};

let authInstance: WebAuth | FirebaseAuthTypes.Module;

if (Platform.OS === 'web') {
  // Web platform: use Firebase Web SDK
  if (getApps().length === 0) {
    initializeApp(firebaseConfig);
  }
  authInstance = getWebAuth();
} else {
  // Mobile platforms: use React Native Firebase
  // No manual initialization needed - reads from google-services.json
  authInstance = auth();
}

export { authInstance as auth };
export default authInstance;
